/*
*********************************************************************************************************
                                               _04_OS
    File			 : main.c
    By  			 : Muhe
    platform   : STM32F407ZG
	Data   		 : 2018/7/16
    Note       ：因为时间有限，只实现了简单的多线程，可能存在部分BUG，如果发现BUG麻烦联系QQ 1145474846
*********************************************************************************************************
*/
#include "User_header.h"
#include "User.h"

/*
***********************************************************************************************************

 											任务堆栈定区（每个任务都需要开辟一个特定的堆栈）

				Note:	堆栈大小根据任务局部变量多少定，单片机rom有限，尽量合理分配任务栈堆大小

***********************************************************************************************************
*/
unsigned int TASK_0_STK[9000];
unsigned int TASK_1_STK[512];
//unsigned int TASK_2_STK[64];
//unsigned int TASK_3_STK[256];
unsigned int TASK_4_STK[256];
unsigned int TASK_5_STK[64];
/*
************************************************************************************************************

																函数定义区

************************************************************************************************************
*/



void OS_Init()
{
	System_init();
	LED_Init();
	
	PS2_Keyboard_Init();

	OS_LCD_Init();
	//Touch_Init(); 
	
}

int main()
{
	
	Task_Create(User_main,&TASK_0_STK[9000-1],1);
	Task_Create(MyPs2KeyScan,&TASK_1_STK[512-1],0);
	Task_Create(LED_main,&TASK_5_STK[64-1],3);
	Task_Create(run_direct,&TASK_4_STK[256-1],2);
	
	//OSTaskSuspend(5);    //挂起任务
	OS_Init();
	OS_Start();
}


/************************************************* (C) COPYLEFT 2018 Muhe  *****************************************************/
