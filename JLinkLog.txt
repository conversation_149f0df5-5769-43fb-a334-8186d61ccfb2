T749C 000:009.064   SEGGER J-Link V8.16 Log File
T749C 000:009.216   DLL Compiled: Feb 26 2025 12:07:26
T749C 000:009.223   Logging started @ 2025-07-31 14:10
T749C 000:009.229   Process: D:\Keil_v5\UV4\UV4.exe
T749C 000:009.245 - 9.235ms 
T749C 000:009.257 JLINK_SetWarnOutHandler(...)
T749C 000:009.263 - 0.007ms 
T749C 000:009.274 JLINK_OpenEx(...)
T749C 000:012.167   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T749C 000:012.676   Firmware: J-Link V9 compiled May  7 2021 16:26:12
T749C 000:012.828   Decompressing FW timestamp took 129 us
T749C 000:019.034   Hardware: V9.40
T749C 000:019.073   S/N: 69402827
T749C 000:019.084   OEM: SEGGER
T749C 000:019.095   Feature(s): R<PERSON>, GDB, FlashDL, FlashB<PERSON>, JFlash
T749C 000:019.709   Bootloader: (FW returned invalid version)
T749C 000:020.472   TELNET listener socket opened on port 19021
T749C 000:020.696   WEBSRV WEBSRV_Init(): Starting webserver thread(s)
T749C 000:020.954   WEBSRV Webserver running on local port 19080
T749C 000:021.137   Looking for J-Link GUI Server exe at: D:\Keil_v5\ARM\Segger\JLinkGUIServer.exe
T749C 000:021.311   Looking for J-Link GUI Server exe at: C:\Program Files (x86)\SEGGER\JLink_V634b\\JLinkGUIServer.exe
T749C 000:323.431   Failed to connect to J-Link GUI Server.
T749C 000:323.479 - 314.193ms returns "O.K."
T749C 000:323.500 JLINK_GetEmuCaps()
T749C 000:323.510 - 0.007ms returns 0xB9FF7BBF
T749C 000:323.521 JLINK_TIF_GetAvailable(...)
T749C 000:323.675 - 0.154ms 
T749C 000:323.702 JLINK_SetErrorOutHandler(...)
T749C 000:323.708 - 0.006ms 
T749C 000:323.742 JLINK_ExecCommand("ProjectFile = "E:\study\competition\demo\JLinkSettings.ini"", ...). 
T749C 000:335.280 - 11.543ms returns 0x00
T749C 000:335.326 JLINK_ExecCommand("Device = STM32F407ZGTx", ...). 
T749C 000:336.303   Device "STM32F407ZG" selected.
T749C 000:336.659 - 1.326ms returns 0x00
T749C 000:336.670 JLINK_ExecCommand("DisableConnectionTimeout", ...). 
T749C 000:336.685   ERROR: Unknown command
T749C 000:336.691 - 0.014ms returns 0x01
T749C 000:336.697 JLINK_GetHardwareVersion()
T749C 000:336.701 - 0.004ms returns 94000
T749C 000:336.707 JLINK_GetDLLVersion()
T749C 000:336.711 - 0.004ms returns 81600
T749C 000:336.716 JLINK_GetOEMString(...)
T749C 000:336.721 JLINK_GetFirmwareString(...)
T749C 000:336.725 - 0.004ms 
T749C 000:336.740 JLINK_GetDLLVersion()
T749C 000:336.745 - 0.004ms returns 81600
T749C 000:336.750 JLINK_GetCompileDateTime()
T749C 000:336.754 - 0.004ms 
T749C 000:336.761 JLINK_GetFirmwareString(...)
T749C 000:336.766 - 0.004ms 
T749C 000:336.773 JLINK_GetHardwareVersion()
T749C 000:336.777 - 0.004ms returns 94000
T749C 000:336.784 JLINK_GetSN()
T749C 000:336.788 - 0.004ms returns 69402827
T749C 000:336.795 JLINK_GetOEMString(...)
T749C 000:336.809 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T749C 000:337.292 - 0.483ms returns 0x00
T749C 000:337.299 JLINK_HasError()
T749C 000:337.311 JLINK_SetSpeed(500)
T749C 000:337.412 - 0.103ms 
T749C 000:337.419 JLINK_GetId()
T749C 000:337.607   InitTarget() start
T749C 000:337.618    J-Link Script File: Executing InitTarget()
T749C 000:337.757   SWD selected. Executing JTAG -> SWD switching sequence.
T749C 000:340.813   DAP initialized successfully.
T749C 000:349.619   InitTarget() end - Took 11.9ms
T749C 000:350.842   Found SW-DP with ID 0x2BA01477
T749C 000:353.140   DAP: Could not power-up system power domain.
T749C 000:355.695   DAP error while determining CoreSight SoC version
T749C 000:402.613   InitTarget() start
T749C 000:402.647    J-Link Script File: Executing InitTarget()
T749C 000:402.848   SWD selected. Executing JTAG -> SWD switching sequence.
T749C 000:406.131   DAP initialized successfully.
T749C 000:414.586   InitTarget() end - Took 11.9ms
T749C 000:415.774   Found SW-DP with ID 0x2BA01477
T749C 000:419.626   DPIDR: 0x2BA01477
T749C 000:419.647   CoreSight SoC-400 or earlier
T749C 000:419.676   Scanning AP map to find all available APs
T749C 000:420.957   AP[1]: Stopped AP scan as end of AP map has been reached
T749C 000:420.985   AP[0]: AHB-AP (IDR: 0x24770011, ADDR: 0x00000000)
T749C 000:421.073   Iterating through AP map to find AHB-AP to use
T749C 000:423.062   AP[0]: Core found
T749C 000:423.079   AP[0]: AHB-AP ROM base: 0xE00FF000
T749C 000:424.844   CPUID register: 0x410FC241. Implementer code: 0x41 (ARM)
T749C 000:424.889   Found Cortex-M4 r0p1, Little endian.
T749C 000:425.177   -- Max. mem block: 0x00010E60
T749C 000:425.729   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T749C 000:427.527   CPU_ReadMem(4 bytes @ 0x********)
T749C 000:429.298   FPUnit: 6 code (BP) slots and 2 literal slots
T749C 000:429.311   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T749C 000:431.050   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T749C 000:432.074   CPU_ReadMem(4 bytes @ 0xE0001000)
T749C 000:433.833   CPU_WriteMem(4 bytes @ 0xE0001000)
T749C 000:434.837   CPU_ReadMem(4 bytes @ 0xE000ED88)
T749C 000:436.577   CPU_WriteMem(4 bytes @ 0xE000ED88)
T749C 000:437.593   CPU_ReadMem(4 bytes @ 0xE000ED88)
T749C 000:439.339   CPU_WriteMem(4 bytes @ 0xE000ED88)
T749C 000:440.381   CoreSight components:
T749C 000:440.406   ROMTbl[0] @ E00FF000
T749C 000:440.418   CPU_ReadMem(64 bytes @ 0xE00FF000)
T749C 000:444.223   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T749C 000:446.944   [0][0]: E000E000 CID B105E00D PID 000BB00C SCS-M7
T749C 000:446.957   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T749C 000:449.663   [0][1]: E0001000 CID B105E00D PID 003BB002 DWT
T749C 000:449.675   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T749C 000:452.379   [0][2]: ******** CID B105E00D PID 002BB003 FPB
T749C 000:452.401   CPU_ReadMem(32 bytes @ 0xE0000FE0)
T749C 000:455.130   [0][3]: ******** CID B105E00D PID 003BB001 ITM
T749C 000:455.144   CPU_ReadMem(32 bytes @ 0xE0040FE0)
T749C 000:457.926   [0][4]: ******** CID B105900D PID 000BB9A1 TPIU
T749C 000:457.949   CPU_ReadMem(32 bytes @ 0xE0041FE0)
T749C 000:460.694   [0][5]: ******** CID B105900D PID 000BB925 ETM
T749C 000:461.109 - 123.688ms returns 0x2BA01477
T749C 000:461.197 JLINK_GetDLLVersion()
T749C 000:461.206 - 0.008ms returns 81600
T749C 000:461.229 JLINK_CORE_GetFound()
T749C 000:461.236 - 0.007ms returns 0xE0000FF
T749C 000:461.246 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T749C 000:461.257   Value=0xE00FF000
T749C 000:461.268 - 0.022ms returns 0
T749C 000:461.289 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T749C 000:461.297   Value=0xE00FF000
T749C 000:461.307 - 0.018ms returns 0
T749C 000:461.316 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
T749C 000:461.323   Value=0x********
T749C 000:461.333 - 0.017ms returns 0
T749C 000:461.342 JLINK_ReadMemEx(0xE0041FD0, 0x20 Bytes, Flags = 0x02000004)
T749C 000:461.384   CPU_ReadMem(32 bytes @ 0xE0041FD0)
T749C 000:464.133   Data:  04 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T749C 000:464.148 - 2.806ms returns 32 (0x20)
T749C 000:464.159 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
T749C 000:464.168   Value=0x00000000
T749C 000:464.178 - 0.019ms returns 0
T749C 000:464.187 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
T749C 000:464.194   Value=0x********
T749C 000:464.204 - 0.017ms returns 0
T749C 000:464.213 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
T749C 000:464.220   Value=0x********
T749C 000:464.230 - 0.018ms returns 0
T749C 000:464.239 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
T749C 000:464.246   Value=0xE0001000
T749C 000:464.256 - 0.018ms returns 0
T749C 000:464.265 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
T749C 000:464.272   Value=0x********
T749C 000:464.282 - 0.017ms returns 0
T749C 000:464.291 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
T749C 000:464.299   Value=0xE000E000
T749C 000:464.309 - 0.018ms returns 0
T749C 000:464.318 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
T749C 000:464.325   Value=0xE000EDF0
T749C 000:464.335 - 0.017ms returns 0
T749C 000:464.343 JLINK_GetDebugInfo(0x01 = Unknown)
T749C 000:464.350   Value=0x00000001
T749C 000:464.360 - 0.017ms returns 0
T749C 000:464.369 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T749C 000:464.388   CPU_ReadMem(4 bytes @ 0xE000ED00)
T749C 000:466.170   Data:  41 C2 0F 41
T749C 000:466.202   Debug reg: CPUID
T749C 000:466.216 - 1.847ms returns 1 (0x1)
T749C 000:466.228 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
T749C 000:466.235   Value=0x00000000
T749C 000:466.246 - 0.019ms returns 0
T749C 000:466.256 JLINK_HasError()
T749C 000:466.267 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T749C 000:466.275 - 0.008ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T749C 000:466.284 JLINK_Reset()
T749C 000:466.303   JLINK_GetResetTypeDesc
T749C 000:466.313   - 0.010ms 
T749C 000:466.347   Reset type: NORMAL (https://wiki.segger.com/J-Link_Reset_Strategies)
T749C 000:466.364   CPU is running
T749C 000:466.376   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T749C 000:467.427   CPU is running
T749C 000:467.447   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T749C 000:468.545   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T749C 000:469.068   Reset: Reset device via AIRCR.SYSRESETREQ.
T749C 000:469.087   CPU is running
T749C 000:469.098   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T749C 000:523.216   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T749C 000:525.002   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T749C 000:557.260   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T749C 000:564.195   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T749C 000:596.328   CPU_WriteMem(4 bytes @ 0x********)
T749C 000:597.440   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T749C 000:599.202   CPU_ReadMem(4 bytes @ 0xE0001000)
T749C 000:600.970 - 134.686ms 
T749C 000:601.018 JLINK_HasError()
T749C 000:601.038 JLINK_ReadReg(R15 (PC))
T749C 000:601.046 - 0.020ms returns 0x08000228
T749C 000:601.052 JLINK_ReadReg(XPSR)
T749C 000:601.057 - 0.005ms returns 0x01000000
T749C 000:601.064 JLINK_Halt()
T749C 000:601.069 - 0.004ms returns 0x00
T749C 000:601.075 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T749C 000:601.084   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T749C 000:602.839   Data:  03 00 03 00
T749C 000:602.858   Debug reg: DHCSR
T749C 000:602.868 - 1.792ms returns 1 (0x1)
T749C 000:602.877 JLINK_WriteU32(0xE000EDF0, 0xA05F0003)
T749C 000:602.884   Debug reg: DHCSR
T749C 000:603.198   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T749C 000:604.222 - 1.344ms returns 0 (0x00000000)
T749C 000:604.240 JLINK_WriteU32(0xE000EDFC, 0x01000000)
T749C 000:604.247   Debug reg: DEMCR
T749C 000:604.261   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T749C 000:605.265 - 1.025ms returns 0 (0x00000000)
T749C 000:605.323 JLINK_GetHWStatus(...)
T749C 000:605.512 - 0.188ms returns 0
T749C 000:605.538 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T749C 000:605.545 - 0.008ms returns 0x06
T749C 000:605.554 JLINK_GetNumBPUnits(Type = 0xF0)
T749C 000:605.562 - 0.007ms returns 0x2000
T749C 000:605.570 JLINK_GetNumWPUnits()
T749C 000:605.577 - 0.007ms returns 4
T749C 000:605.595 JLINK_GetSpeed()
T749C 000:605.603 - 0.007ms returns 500
T749C 000:605.616 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T749C 000:605.627   CPU_ReadMem(4 bytes @ 0xE000E004)
T749C 000:607.387   Data:  02 00 00 00
T749C 000:607.407 - 1.790ms returns 1 (0x1)
T749C 000:607.418 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T749C 000:607.430   CPU_ReadMem(4 bytes @ 0xE000E004)
T749C 000:609.197   Data:  02 00 00 00
T749C 000:609.216 - 1.798ms returns 1 (0x1)
T749C 000:609.227 JLINK_WriteMemEx(0xE0001000, 0x0000001C Bytes, Flags = 0x02000004)
T749C 000:609.233   Data:  01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 ...
T749C 000:609.248   CPU_WriteMem(28 bytes @ 0xE0001000)
T749C 000:611.079 - 1.851ms returns 0x1C
T749C 000:611.095 JLINK_HasError()
T749C 000:611.104 JLINK_ReadReg(R15 (PC))
T749C 000:611.112 - 0.008ms returns 0x08000228
T749C 000:611.119 JLINK_ReadReg(XPSR)
T749C 000:611.126 - 0.006ms returns 0x01000000
T749C 000:613.746 JLINK_ReadMemEx(0xE0001004, 0x4 Bytes, Flags = 0x02000000)
T749C 000:613.760   Data:  00 00 00 00
T749C 000:613.768   Debug reg: DWT_CYCCNT
T749C 000:613.775 - 0.028ms returns 4 (0x4)
T749C 000:708.903 JLINK_HasError()
T749C 000:708.942 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T749C 000:708.962 - 0.020ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T749C 000:708.969 JLINK_Reset()
T749C 000:708.988   JLINK_GetResetTypeDesc
T749C 000:708.996   - 0.008ms 
T749C 000:709.024   Reset type: NORMAL (https://wiki.segger.com/J-Link_Reset_Strategies)
T749C 000:709.036   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T749C 000:710.105   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T749C 000:711.114   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T749C 000:711.601   Reset: Reset device via AIRCR.SYSRESETREQ.
T749C 000:711.626   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T749C 000:765.801   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T749C 000:767.570   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T749C 000:769.345   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T749C 000:775.518   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T749C 000:806.946   CPU_WriteMem(4 bytes @ 0x********)
T749C 000:808.038   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T749C 000:809.810   CPU_ReadMem(4 bytes @ 0xE0001000)
T749C 000:811.581 - 102.611ms 
T749C 000:811.711 JLINK_HasError()
T749C 000:811.721 JLINK_ReadReg(R15 (PC))
T749C 000:811.729 - 0.008ms returns 0x08000228
T749C 000:811.735 JLINK_ReadReg(XPSR)
T749C 000:811.740 - 0.005ms returns 0x01000000
T749C 000:812.077 JLINK_ReadMemEx(0x08000188, 0x3C Bytes, Flags = 0x02000000)
T749C 000:812.092   CPU_ReadMem(128 bytes @ 0x08000180)
T749C 000:818.097    -- Updating C cache (128 bytes @ 0x08000180)
T749C 000:818.122    -- Read from C cache (60 bytes @ 0x08000188)
T749C 000:818.137   Data:  00 F0 02 F8 00 F0 3C F8 0A A0 90 E8 00 0C 82 44 ...
T749C 000:818.148 - 6.071ms returns 60 (0x3C)
T749C 000:818.160 JLINK_ReadMemEx(0x08000188, 0x2 Bytes, Flags = 0x02000000)
T749C 000:818.170    -- Read from C cache (2 bytes @ 0x08000188)
T749C 000:818.180   Data:  00 F0
T749C 000:818.192 - 0.031ms returns 2 (0x2)
T749C 000:818.316 JLINK_ReadMemEx(0x0800018A, 0x2 Bytes, Flags = 0x02000000)
T749C 000:818.324    -- Read from C cache (2 bytes @ 0x0800018A)
T749C 000:818.334   Data:  02 F8
T749C 000:818.345 - 0.029ms returns 2 (0x2)
T749C 000:818.372 JLINK_ReadMemEx(0x0800018C, 0x3C Bytes, Flags = 0x02000000)
T749C 000:818.381    -- Read from C cache (60 bytes @ 0x0800018C)
T749C 000:818.392   Data:  00 F0 3C F8 0A A0 90 E8 00 0C 82 44 83 44 AA F1 ...
T749C 000:818.402 - 0.030ms returns 60 (0x3C)
T749C 000:818.411 JLINK_ReadMemEx(0x0800018C, 0x2 Bytes, Flags = 0x02000000)
T749C 000:818.419    -- Read from C cache (2 bytes @ 0x0800018C)
T749C 000:818.430   Data:  00 F0
T749C 000:818.440 - 0.029ms returns 2 (0x2)
T749C 000:818.453 JLINK_ReadMemEx(0x0800018E, 0x2 Bytes, Flags = 0x02000000)
T749C 000:818.460    -- Read from C cache (2 bytes @ 0x0800018E)
T749C 000:818.471   Data:  3C F8
T749C 000:818.481 - 0.028ms returns 2 (0x2)
T749C 000:818.490 JLINK_ReadMemEx(0x08000190, 0x3C Bytes, Flags = 0x02000000)
T749C 000:818.497    -- Read from C cache (60 bytes @ 0x08000190)
T749C 000:818.508   Data:  0A A0 90 E8 00 0C 82 44 83 44 AA F1 01 07 DA 45 ...
T749C 000:818.534 - 0.044ms returns 60 (0x3C)
T749C 000:818.541 JLINK_ReadMemEx(0x08000190, 0x2 Bytes, Flags = 0x02000000)
T749C 000:818.548    -- Read from C cache (2 bytes @ 0x08000190)
T749C 000:818.556   Data:  0A A0
T749C 000:818.565 - 0.023ms returns 2 (0x2)
T749C 000:818.572 JLINK_ReadMemEx(0x08000192, 0x2 Bytes, Flags = 0x02000000)
T749C 000:818.579    -- Read from C cache (2 bytes @ 0x08000192)
T749C 000:818.587   Data:  90 E8
T749C 000:818.596 - 0.024ms returns 2 (0x2)
T749C 000:818.636 JLINK_ReadMemEx(0x08000192, 0x2 Bytes, Flags = 0x02000000)
T749C 000:818.643    -- Read from C cache (2 bytes @ 0x08000192)
T749C 000:818.651   Data:  90 E8
T749C 000:818.660 - 0.023ms returns 2 (0x2)
T749C 000:818.667 JLINK_ReadMemEx(0x08000194, 0x3C Bytes, Flags = 0x02000000)
T749C 000:818.673    -- Read from C cache (60 bytes @ 0x08000194)
T749C 000:818.683   Data:  00 0C 82 44 83 44 AA F1 01 07 DA 45 01 D1 00 F0 ...
T749C 000:818.691 - 0.024ms returns 60 (0x3C)
T749C 000:818.698 JLINK_ReadMemEx(0x08000194, 0x2 Bytes, Flags = 0x02000000)
T749C 000:818.758    -- Read from C cache (2 bytes @ 0x08000194)
T749C 000:818.769   Data:  00 0C
T749C 000:818.777 - 0.079ms returns 2 (0x2)
T749C 000:818.785 JLINK_ReadMemEx(0x08000196, 0x2 Bytes, Flags = 0x02000000)
T749C 000:818.791    -- Read from C cache (2 bytes @ 0x08000196)
T749C 000:818.800   Data:  82 44
T749C 000:818.808 - 0.023ms returns 2 (0x2)
T749C 000:818.816 JLINK_ReadMemEx(0x08000198, 0x3C Bytes, Flags = 0x02000000)
T749C 000:818.822    -- Read from C cache (60 bytes @ 0x08000198)
T749C 000:818.831   Data:  83 44 AA F1 01 07 DA 45 01 D1 00 F0 31 F8 AF F2 ...
T749C 000:818.839 - 0.023ms returns 60 (0x3C)
T749C 000:818.847 JLINK_ReadMemEx(0x08000198, 0x2 Bytes, Flags = 0x02000000)
T749C 000:818.853    -- Read from C cache (2 bytes @ 0x08000198)
T749C 000:818.861   Data:  83 44
T749C 000:818.870 - 0.023ms returns 2 (0x2)
T749C 000:818.877 JLINK_ReadMemEx(0x08000198, 0x3C Bytes, Flags = 0x02000000)
T749C 000:818.883    -- Read from C cache (60 bytes @ 0x08000198)
T749C 000:818.892   Data:  83 44 AA F1 01 07 DA 45 01 D1 00 F0 31 F8 AF F2 ...
T749C 000:818.901 - 0.023ms returns 60 (0x3C)
T749C 000:818.908 JLINK_ReadMemEx(0x08000198, 0x2 Bytes, Flags = 0x02000000)
T749C 000:818.914    -- Read from C cache (2 bytes @ 0x08000198)
T749C 000:818.922   Data:  83 44
T749C 000:818.931 - 0.023ms returns 2 (0x2)
T749C 000:818.938 JLINK_ReadMemEx(0x0800019A, 0x2 Bytes, Flags = 0x02000000)
T749C 000:818.944    -- Read from C cache (2 bytes @ 0x0800019A)
T749C 000:818.953   Data:  AA F1
T749C 000:818.961 - 0.023ms returns 2 (0x2)
T749C 000:818.969 JLINK_ReadMemEx(0x0800019A, 0x2 Bytes, Flags = 0x02000000)
T749C 000:818.975    -- Read from C cache (2 bytes @ 0x0800019A)
T749C 000:818.984   Data:  AA F1
T749C 000:818.992 - 0.023ms returns 2 (0x2)
T749C 000:818.999 JLINK_ReadMemEx(0x0800019C, 0x3C Bytes, Flags = 0x02000000)
T749C 000:819.005    -- Read from C cache (60 bytes @ 0x0800019C)
T749C 000:819.015   Data:  01 07 DA 45 01 D1 00 F0 31 F8 AF F2 09 0E BA E8 ...
T749C 000:819.023 - 0.024ms returns 60 (0x3C)
T749C 000:819.030 JLINK_ReadMemEx(0x0800019C, 0x2 Bytes, Flags = 0x02000000)
T749C 000:819.036    -- Read from C cache (2 bytes @ 0x0800019C)
T749C 000:819.045   Data:  01 07
T749C 000:819.053 - 0.023ms returns 2 (0x2)
T749C 000:819.068 JLINK_ReadMemEx(0x0800019E, 0x2 Bytes, Flags = 0x02000000)
T749C 000:819.074    -- Read from C cache (2 bytes @ 0x0800019E)
T749C 000:819.083   Data:  DA 45
T749C 000:819.092 - 0.023ms returns 2 (0x2)
T749C 000:819.099 JLINK_ReadMemEx(0x080001A0, 0x3C Bytes, Flags = 0x02000000)
T749C 000:819.105    -- Read from C cache (60 bytes @ 0x080001A0)
T749C 000:819.114   Data:  01 D1 00 F0 31 F8 AF F2 09 0E BA E8 0F 00 13 F0 ...
T749C 000:819.123 - 0.024ms returns 60 (0x3C)
T749C 000:819.130 JLINK_ReadMemEx(0x080001A0, 0x2 Bytes, Flags = 0x02000000)
T749C 000:819.136    -- Read from C cache (2 bytes @ 0x080001A0)
T749C 000:819.145   Data:  01 D1
T749C 000:819.154 - 0.023ms returns 2 (0x2)
T749C 000:819.161 JLINK_ReadMemEx(0x080001A0, 0x3C Bytes, Flags = 0x02000000)
T749C 000:819.167    -- Read from C cache (60 bytes @ 0x080001A0)
T749C 000:819.176   Data:  01 D1 00 F0 31 F8 AF F2 09 0E BA E8 0F 00 13 F0 ...
T749C 000:819.185 - 0.024ms returns 60 (0x3C)
T749C 000:819.192 JLINK_ReadMemEx(0x080001A0, 0x2 Bytes, Flags = 0x02000000)
T749C 000:819.198    -- Read from C cache (2 bytes @ 0x080001A0)
T749C 000:819.207   Data:  01 D1
T749C 000:819.215 - 0.023ms returns 2 (0x2)
T749C 000:819.222 JLINK_ReadMemEx(0x080001A2, 0x2 Bytes, Flags = 0x02000000)
T749C 000:819.229    -- Read from C cache (2 bytes @ 0x080001A2)
T749C 000:819.237   Data:  00 F0
T749C 000:819.246 - 0.023ms returns 2 (0x2)
T749C 000:819.253 JLINK_ReadMemEx(0x080001A2, 0x2 Bytes, Flags = 0x02000000)
T749C 000:819.259    -- Read from C cache (2 bytes @ 0x080001A2)
T749C 000:819.268   Data:  00 F0
T749C 000:819.276 - 0.023ms returns 2 (0x2)
T749C 000:819.283 JLINK_ReadMemEx(0x080001A4, 0x3C Bytes, Flags = 0x02000000)
T749C 000:819.290    -- Read from C cache (60 bytes @ 0x080001A4)
T749C 000:819.301   Data:  31 F8 AF F2 09 0E BA E8 0F 00 13 F0 01 0F 18 BF ...
T749C 000:819.310 - 0.026ms returns 60 (0x3C)
T749C 000:819.317 JLINK_ReadMemEx(0x080001A4, 0x2 Bytes, Flags = 0x02000000)
T749C 000:819.323    -- Read from C cache (2 bytes @ 0x080001A4)
T749C 000:819.331   Data:  31 F8
T749C 000:819.340 - 0.023ms returns 2 (0x2)
T749C 000:819.347 JLINK_ReadMemEx(0x080001A6, 0x2 Bytes, Flags = 0x02000000)
T749C 000:819.354    -- Read from C cache (2 bytes @ 0x080001A6)
T749C 000:819.362   Data:  AF F2
T749C 000:819.371 - 0.023ms returns 2 (0x2)
T749C 000:819.378 JLINK_ReadMemEx(0x080001A8, 0x3C Bytes, Flags = 0x02000000)
T749C 000:819.384    -- Read from C cache (60 bytes @ 0x080001A8)
T749C 000:819.393   Data:  09 0E BA E8 0F 00 13 F0 01 0F 18 BF FB 1A 43 F0 ...
T749C 000:819.402 - 0.024ms returns 60 (0x3C)
T749C 000:819.409 JLINK_ReadMemEx(0x080001A8, 0x2 Bytes, Flags = 0x02000000)
T749C 000:819.415    -- Read from C cache (2 bytes @ 0x080001A8)
T749C 000:819.424   Data:  09 0E
T749C 000:819.432 - 0.023ms returns 2 (0x2)
T749C 000:819.440 JLINK_ReadMemEx(0x080001AA, 0x2 Bytes, Flags = 0x02000000)
T749C 000:819.446    -- Read from C cache (2 bytes @ 0x080001AA)
T749C 000:819.454   Data:  BA E8
T749C 000:819.463 - 0.023ms returns 2 (0x2)
T749C 000:819.470 JLINK_ReadMemEx(0x080001AC, 0x3C Bytes, Flags = 0x02000000)
T749C 000:819.476    -- Read from C cache (60 bytes @ 0x080001AC)
T749C 000:819.486   Data:  0F 00 13 F0 01 0F 18 BF FB 1A 43 F0 01 03 18 47 ...
T749C 000:819.494 - 0.024ms returns 60 (0x3C)
T749C 000:819.501 JLINK_ReadMemEx(0x080001AC, 0x2 Bytes, Flags = 0x02000000)
T749C 000:819.508    -- Read from C cache (2 bytes @ 0x080001AC)
T749C 000:819.516   Data:  0F 00
T749C 000:819.524 - 0.023ms returns 2 (0x2)
T749C 000:819.532 JLINK_ReadMemEx(0x080001AE, 0x2 Bytes, Flags = 0x02000000)
T749C 000:819.538    -- Read from C cache (2 bytes @ 0x080001AE)
T749C 000:819.546   Data:  13 F0
T749C 000:819.555 - 0.023ms returns 2 (0x2)
T749C 000:819.562 JLINK_ReadMemEx(0x080001B0, 0x3C Bytes, Flags = 0x02000000)
T749C 000:819.568    -- Read from C cache (60 bytes @ 0x080001B0)
T749C 000:819.577   Data:  01 0F 18 BF FB 1A 43 F0 01 03 18 47 30 DD 00 00 ...
T749C 000:819.586 - 0.024ms returns 60 (0x3C)
T749C 000:819.593 JLINK_ReadMemEx(0x080001B0, 0x2 Bytes, Flags = 0x02000000)
T749C 000:819.599    -- Read from C cache (2 bytes @ 0x080001B0)
T749C 000:819.608   Data:  01 0F
T749C 000:819.616 - 0.023ms returns 2 (0x2)
T749C 000:819.623 JLINK_ReadMemEx(0x080001B2, 0x2 Bytes, Flags = 0x02000000)
T749C 000:819.630    -- Read from C cache (2 bytes @ 0x080001B2)
T749C 000:819.639   Data:  18 BF
T749C 000:819.649 - 0.025ms returns 2 (0x2)
T749C 000:819.654 JLINK_ReadMemEx(0x080001B4, 0x3C Bytes, Flags = 0x02000000)
T749C 000:819.659    -- Read from C cache (60 bytes @ 0x080001B4)
T749C 000:819.665   Data:  FB 1A 43 F0 01 03 18 47 30 DD 00 00 50 DD 00 00 ...
T749C 000:819.672 - 0.018ms returns 60 (0x3C)
T749C 000:819.677 JLINK_ReadMemEx(0x080001B4, 0x2 Bytes, Flags = 0x02000000)
T749C 000:819.682    -- Read from C cache (2 bytes @ 0x080001B4)
T749C 000:819.689   Data:  FB 1A
T749C 000:819.695 - 0.017ms returns 2 (0x2)
T749C 000:819.701 JLINK_ReadMemEx(0x080001B4, 0x3C Bytes, Flags = 0x02000000)
T749C 000:819.706    -- Read from C cache (60 bytes @ 0x080001B4)
T749C 000:819.713   Data:  FB 1A 43 F0 01 03 18 47 30 DD 00 00 50 DD 00 00 ...
T749C 000:819.720 - 0.018ms returns 60 (0x3C)
T749C 000:819.725 JLINK_ReadMemEx(0x080001B4, 0x2 Bytes, Flags = 0x02000000)
T749C 000:819.730    -- Read from C cache (2 bytes @ 0x080001B4)
T749C 000:819.737   Data:  FB 1A
T749C 000:819.743 - 0.018ms returns 2 (0x2)
T749C 000:819.751 JLINK_ReadMemEx(0x080001B6, 0x2 Bytes, Flags = 0x02000000)
T749C 000:819.769    -- Read from C cache (2 bytes @ 0x080001B6)
T749C 000:819.777   Data:  43 F0
T749C 000:819.786 - 0.035ms returns 2 (0x2)
T749C 000:819.793 JLINK_ReadMemEx(0x080001B6, 0x2 Bytes, Flags = 0x02000000)
T749C 000:819.799    -- Read from C cache (2 bytes @ 0x080001B6)
T749C 000:819.810   Data:  43 F0
T749C 000:819.818 - 0.024ms returns 2 (0x2)
T749C 000:819.825 JLINK_ReadMemEx(0x080001B8, 0x3C Bytes, Flags = 0x02000000)
T749C 000:819.831    -- Read from C cache (60 bytes @ 0x080001B8)
T749C 000:819.839   Data:  01 03 18 47 30 DD 00 00 50 DD 00 00 10 3A 24 BF ...
T749C 000:819.848 - 0.022ms returns 60 (0x3C)
T749C 000:819.854 JLINK_ReadMemEx(0x080001B8, 0x2 Bytes, Flags = 0x02000000)
T749C 000:819.860    -- Read from C cache (2 bytes @ 0x080001B8)
T749C 000:819.868   Data:  01 03
T749C 000:819.877 - 0.022ms returns 2 (0x2)
T749C 000:819.883 JLINK_ReadMemEx(0x080001BA, 0x2 Bytes, Flags = 0x02000000)
T749C 000:819.889    -- Read from C cache (2 bytes @ 0x080001BA)
T749C 000:819.897   Data:  18 47
T749C 000:819.906 - 0.022ms returns 2 (0x2)
T749C 000:819.912 JLINK_ReadMemEx(0x080001BC, 0x3C Bytes, Flags = 0x02000000)
T749C 000:819.918    -- Read from C cache (60 bytes @ 0x080001BC)
T749C 000:819.927   Data:  30 DD 00 00 50 DD 00 00 10 3A 24 BF 78 C8 78 C1 ...
T749C 000:819.935 - 0.022ms returns 60 (0x3C)
T749C 000:819.942 JLINK_ReadMemEx(0x080001BC, 0x2 Bytes, Flags = 0x02000000)
T749C 000:819.948    -- Read from C cache (2 bytes @ 0x080001BC)
T749C 000:819.956   Data:  30 DD
T749C 000:819.964 - 0.022ms returns 2 (0x2)
T749C 000:819.972 JLINK_ReadMemEx(0x080001C4, 0x3C Bytes, Flags = 0x02000000)
T749C 000:819.978    -- Read from C cache (60 bytes @ 0x080001C4)
T749C 000:819.992   Data:  10 3A 24 BF 78 C8 78 C1 FA D8 52 07 24 BF 30 C8 ...
T749C 000:820.003 - 0.031ms returns 60 (0x3C)
T749C 000:820.010 JLINK_ReadMemEx(0x080001C4, 0x2 Bytes, Flags = 0x02000000)
T749C 000:820.016    -- Read from C cache (2 bytes @ 0x080001C4)
T749C 000:820.024   Data:  10 3A
T749C 000:820.032 - 0.022ms returns 2 (0x2)
T749C 000:820.039 JLINK_ReadMemEx(0x080001C6, 0x2 Bytes, Flags = 0x02000000)
T749C 000:820.045    -- Read from C cache (2 bytes @ 0x080001C6)
T749C 000:820.053   Data:  24 BF
T749C 000:820.061 - 0.022ms returns 2 (0x2)
T749C 000:820.068 JLINK_ReadMemEx(0x080001C6, 0x2 Bytes, Flags = 0x02000000)
T749C 000:820.074    -- Read from C cache (2 bytes @ 0x080001C6)
T749C 000:820.082   Data:  24 BF
T749C 000:820.091 - 0.022ms returns 2 (0x2)
T749C 000:820.098 JLINK_ReadMemEx(0x080001C8, 0x3C Bytes, Flags = 0x02000000)
T749C 000:820.105   CPU_ReadMem(64 bytes @ 0x08000200)
T749C 000:823.933    -- Updating C cache (64 bytes @ 0x08000200)
T749C 000:823.952    -- Read from C cache (60 bytes @ 0x080001C8)
T749C 000:823.962   Data:  78 C8 78 C1 FA D8 52 07 24 BF 30 C8 30 C1 44 BF ...
T749C 000:823.971 - 3.873ms returns 60 (0x3C)
T749C 000:823.981 JLINK_ReadMemEx(0x080001C8, 0x2 Bytes, Flags = 0x02000000)
T749C 000:823.989    -- Read from C cache (2 bytes @ 0x080001C8)
T749C 000:823.997   Data:  78 C8
T749C 000:824.006 - 0.025ms returns 2 (0x2)
T749C 000:824.015 JLINK_ReadMemEx(0x080001C8, 0x3C Bytes, Flags = 0x02000000)
T749C 000:824.022    -- Read from C cache (60 bytes @ 0x080001C8)
T749C 000:824.031   Data:  78 C8 78 C1 FA D8 52 07 24 BF 30 C8 30 C1 44 BF ...
T749C 000:824.039 - 0.024ms returns 60 (0x3C)
T749C 000:824.047 JLINK_ReadMemEx(0x080001C8, 0x2 Bytes, Flags = 0x02000000)
T749C 000:824.053    -- Read from C cache (2 bytes @ 0x080001C8)
T749C 000:824.062   Data:  78 C8
T749C 000:824.070 - 0.023ms returns 2 (0x2)
T749C 000:824.078 JLINK_ReadMemEx(0x080001CA, 0x2 Bytes, Flags = 0x02000000)
T749C 000:824.084    -- Read from C cache (2 bytes @ 0x080001CA)
T749C 000:824.093   Data:  78 C1
T749C 000:824.101 - 0.023ms returns 2 (0x2)
T749C 000:824.109 JLINK_ReadMemEx(0x080001CA, 0x2 Bytes, Flags = 0x02000000)
T749C 000:824.117    -- Read from C cache (2 bytes @ 0x080001CA)
T749C 000:824.129   Data:  78 C1
T749C 000:824.138 - 0.029ms returns 2 (0x2)
T749C 000:824.145 JLINK_ReadMemEx(0x080001CC, 0x3C Bytes, Flags = 0x02000000)
T749C 000:824.152    -- Read from C cache (60 bytes @ 0x080001CC)
T749C 000:824.161   Data:  FA D8 52 07 24 BF 30 C8 30 C1 44 BF 04 68 0C 60 ...
T749C 000:824.169 - 0.024ms returns 60 (0x3C)
T749C 000:824.180 JLINK_ReadMemEx(0x080001CC, 0x2 Bytes, Flags = 0x02000000)
T749C 000:824.188    -- Read from C cache (2 bytes @ 0x080001CC)
T749C 000:824.197   Data:  FA D8
T749C 000:824.205 - 0.025ms returns 2 (0x2)
T749C 000:824.213 JLINK_ReadMemEx(0x080001CC, 0x3C Bytes, Flags = 0x02000000)
T749C 000:824.219    -- Read from C cache (60 bytes @ 0x080001CC)
T749C 000:824.229   Data:  FA D8 52 07 24 BF 30 C8 30 C1 44 BF 04 68 0C 60 ...
T749C 000:824.237 - 0.024ms returns 60 (0x3C)
T749C 000:824.244 JLINK_ReadMemEx(0x080001CC, 0x2 Bytes, Flags = 0x02000000)
T749C 000:824.251    -- Read from C cache (2 bytes @ 0x080001CC)
T749C 000:824.259   Data:  FA D8
T749C 000:824.268 - 0.023ms returns 2 (0x2)
T749C 000:824.275 JLINK_ReadMemEx(0x080001CE, 0x2 Bytes, Flags = 0x02000000)
T749C 000:824.281    -- Read from C cache (2 bytes @ 0x080001CE)
T749C 000:824.290   Data:  52 07
T749C 000:824.299 - 0.023ms returns 2 (0x2)
T749C 000:824.306 JLINK_ReadMemEx(0x080001CE, 0x2 Bytes, Flags = 0x02000000)
T749C 000:824.312    -- Read from C cache (2 bytes @ 0x080001CE)
T749C 000:824.322   Data:  52 07
T749C 000:824.331 - 0.025ms returns 2 (0x2)
T749C 000:824.338 JLINK_ReadMemEx(0x080001D0, 0x3C Bytes, Flags = 0x02000000)
T749C 000:824.344    -- Read from C cache (60 bytes @ 0x080001D0)
T749C 000:824.353   Data:  24 BF 30 C8 30 C1 44 BF 04 68 0C 60 70 47 00 00 ...
T749C 000:824.362 - 0.023ms returns 60 (0x3C)
T749C 000:824.369 JLINK_ReadMemEx(0x080001D0, 0x2 Bytes, Flags = 0x02000000)
T749C 000:824.375    -- Read from C cache (2 bytes @ 0x080001D0)
T749C 000:824.383   Data:  24 BF
T749C 000:824.392 - 0.023ms returns 2 (0x2)
T749C 000:824.399 JLINK_ReadMemEx(0x080001D0, 0x3C Bytes, Flags = 0x02000000)
T749C 000:824.406    -- Read from C cache (60 bytes @ 0x080001D0)
T749C 000:824.415   Data:  24 BF 30 C8 30 C1 44 BF 04 68 0C 60 70 47 00 00 ...
T749C 000:824.438 - 0.038ms returns 60 (0x3C)
T749C 000:824.447 JLINK_ReadMemEx(0x080001D0, 0x2 Bytes, Flags = 0x02000000)
T749C 000:824.454    -- Read from C cache (2 bytes @ 0x080001D0)
T749C 000:824.464   Data:  24 BF
T749C 000:824.474 - 0.028ms returns 2 (0x2)
T749C 000:824.483 JLINK_ReadMemEx(0x080001D2, 0x2 Bytes, Flags = 0x02000000)
T749C 000:824.490    -- Read from C cache (2 bytes @ 0x080001D2)
T749C 000:824.500   Data:  30 C8
T749C 000:824.511 - 0.027ms returns 2 (0x2)
T749C 000:824.519 JLINK_ReadMemEx(0x080001D2, 0x2 Bytes, Flags = 0x02000000)
T749C 000:824.526    -- Read from C cache (2 bytes @ 0x080001D2)
T749C 000:824.537   Data:  30 C8
T749C 000:824.547 - 0.027ms returns 2 (0x2)
T749C 000:824.556 JLINK_ReadMemEx(0x080001D4, 0x3C Bytes, Flags = 0x02000000)
T749C 000:824.563    -- Read from C cache (60 bytes @ 0x080001D4)
T749C 000:824.574   Data:  30 C1 44 BF 04 68 0C 60 70 47 00 00 00 23 00 24 ...
T749C 000:824.585 - 0.029ms returns 60 (0x3C)
T749C 000:824.593 JLINK_ReadMemEx(0x080001D4, 0x2 Bytes, Flags = 0x02000000)
T749C 000:824.600    -- Read from C cache (2 bytes @ 0x080001D4)
T749C 000:824.611   Data:  30 C1
T749C 000:824.621 - 0.028ms returns 2 (0x2)
T749C 000:824.630 JLINK_ReadMemEx(0x080001D4, 0x3C Bytes, Flags = 0x02000000)
T749C 000:824.640    -- Read from C cache (60 bytes @ 0x080001D4)
T749C 000:824.655   Data:  30 C1 44 BF 04 68 0C 60 70 47 00 00 00 23 00 24 ...
T749C 000:824.665 - 0.035ms returns 60 (0x3C)
T749C 000:824.673 JLINK_ReadMemEx(0x080001D4, 0x2 Bytes, Flags = 0x02000000)
T749C 000:824.680    -- Read from C cache (2 bytes @ 0x080001D4)
T749C 000:824.690   Data:  30 C1
T749C 000:824.700 - 0.027ms returns 2 (0x2)
T749C 000:824.708 JLINK_ReadMemEx(0x080001D6, 0x2 Bytes, Flags = 0x02000000)
T749C 000:824.716    -- Read from C cache (2 bytes @ 0x080001D6)
T749C 000:824.726   Data:  44 BF
T749C 000:824.736 - 0.027ms returns 2 (0x2)
T749C 000:824.744 JLINK_ReadMemEx(0x080001D6, 0x2 Bytes, Flags = 0x02000000)
T749C 000:824.752    -- Read from C cache (2 bytes @ 0x080001D6)
T749C 000:824.762   Data:  44 BF
T749C 000:824.772 - 0.027ms returns 2 (0x2)
T749C 000:824.780 JLINK_ReadMemEx(0x080001D8, 0x3C Bytes, Flags = 0x02000000)
T749C 000:824.839    -- Read from C cache (60 bytes @ 0x080001D8)
T749C 000:824.852   Data:  04 68 0C 60 70 47 00 00 00 23 00 24 00 25 00 26 ...
T749C 000:824.862 - 0.082ms returns 60 (0x3C)
T749C 000:824.870 JLINK_ReadMemEx(0x080001D8, 0x2 Bytes, Flags = 0x02000000)
T749C 000:824.878    -- Read from C cache (2 bytes @ 0x080001D8)
T749C 000:824.888   Data:  04 68
T749C 000:824.898 - 0.027ms returns 2 (0x2)
T749C 000:824.907 JLINK_ReadMemEx(0x080001D8, 0x3C Bytes, Flags = 0x02000000)
T749C 000:824.914    -- Read from C cache (60 bytes @ 0x080001D8)
T749C 000:824.925   Data:  04 68 0C 60 70 47 00 00 00 23 00 24 00 25 00 26 ...
T749C 000:824.935 - 0.028ms returns 60 (0x3C)
T749C 000:824.943 JLINK_ReadMemEx(0x080001D8, 0x2 Bytes, Flags = 0x02000000)
T749C 000:824.950    -- Read from C cache (2 bytes @ 0x080001D8)
T749C 000:824.960   Data:  04 68
T749C 000:824.970 - 0.027ms returns 2 (0x2)
T749C 000:824.979 JLINK_ReadMemEx(0x080001DA, 0x2 Bytes, Flags = 0x02000000)
T749C 000:824.986    -- Read from C cache (2 bytes @ 0x080001DA)
T749C 000:824.996   Data:  0C 60
T749C 000:825.006 - 0.027ms returns 2 (0x2)
T749C 000:825.015 JLINK_ReadMemEx(0x080001DA, 0x2 Bytes, Flags = 0x02000000)
T749C 000:825.023    -- Read from C cache (2 bytes @ 0x080001DA)
T749C 000:825.033   Data:  0C 60
T749C 000:825.043 - 0.027ms returns 2 (0x2)
T749C 000:825.051 JLINK_ReadMemEx(0x080001DC, 0x3C Bytes, Flags = 0x02000000)
T749C 000:825.059    -- Read from C cache (60 bytes @ 0x080001DC)
T749C 000:825.069   Data:  70 47 00 00 00 23 00 24 00 25 00 26 10 3A 28 BF ...
T749C 000:825.079 - 0.028ms returns 60 (0x3C)
T749C 000:825.088 JLINK_ReadMemEx(0x080001DC, 0x2 Bytes, Flags = 0x02000000)
T749C 000:825.095    -- Read from C cache (2 bytes @ 0x080001DC)
T749C 000:825.105   Data:  70 47
T749C 000:825.115 - 0.027ms returns 2 (0x2)
T749C 000:825.123 JLINK_ReadMemEx(0x080001DC, 0x3C Bytes, Flags = 0x02000000)
T749C 000:825.130    -- Read from C cache (60 bytes @ 0x080001DC)
T749C 000:825.141   Data:  70 47 00 00 00 23 00 24 00 25 00 26 10 3A 28 BF ...
T749C 000:825.151 - 0.027ms returns 60 (0x3C)
T749C 000:825.159 JLINK_ReadMemEx(0x080001DC, 0x2 Bytes, Flags = 0x02000000)
T749C 000:825.166    -- Read from C cache (2 bytes @ 0x080001DC)
T749C 000:825.176   Data:  70 47
T749C 000:825.187 - 0.027ms returns 2 (0x2)
T749C 000:825.195 JLINK_ReadMemEx(0x080001DE, 0x2 Bytes, Flags = 0x02000000)
T749C 000:825.202    -- Read from C cache (2 bytes @ 0x080001DE)
T749C 000:825.212   Data:  00 00
T749C 000:825.222 - 0.027ms returns 2 (0x2)
T749C 000:825.231 JLINK_ReadMemEx(0x080001DE, 0x2 Bytes, Flags = 0x02000000)
T749C 000:825.238    -- Read from C cache (2 bytes @ 0x080001DE)
T749C 000:825.248   Data:  00 00
T749C 000:825.258 - 0.027ms returns 2 (0x2)
T749C 000:825.267 JLINK_ReadMemEx(0x080001E0, 0x3C Bytes, Flags = 0x02000000)
T749C 000:825.274    -- Read from C cache (60 bytes @ 0x080001E0)
T749C 000:825.285   Data:  00 23 00 24 00 25 00 26 10 3A 28 BF 78 C1 FB D8 ...
T749C 000:825.295 - 0.027ms returns 60 (0x3C)
T749C 000:825.303 JLINK_ReadMemEx(0x080001E0, 0x2 Bytes, Flags = 0x02000000)
T749C 000:825.310    -- Read from C cache (2 bytes @ 0x080001E0)
T749C 000:825.320   Data:  00 23
T749C 000:825.330 - 0.027ms returns 2 (0x2)
T749C 000:825.339 JLINK_ReadMemEx(0x080001E0, 0x3C Bytes, Flags = 0x02000000)
T749C 000:825.346    -- Read from C cache (60 bytes @ 0x080001E0)
T749C 000:825.357   Data:  00 23 00 24 00 25 00 26 10 3A 28 BF 78 C1 FB D8 ...
T749C 000:825.367 - 0.028ms returns 60 (0x3C)
T749C 000:825.375 JLINK_ReadMemEx(0x080001E0, 0x2 Bytes, Flags = 0x02000000)
T749C 000:825.382    -- Read from C cache (2 bytes @ 0x080001E0)
T749C 000:825.392   Data:  00 23
T749C 000:825.403 - 0.027ms returns 2 (0x2)
T749C 000:825.411 JLINK_ReadMemEx(0x080001E2, 0x2 Bytes, Flags = 0x02000000)
T749C 000:825.419    -- Read from C cache (2 bytes @ 0x080001E2)
T749C 000:825.428   Data:  00 24
T749C 000:825.439 - 0.027ms returns 2 (0x2)
T749C 000:825.447 JLINK_ReadMemEx(0x080001E2, 0x2 Bytes, Flags = 0x02000000)
T749C 000:825.457    -- Read from C cache (2 bytes @ 0x080001E2)
T749C 000:825.468   Data:  00 24
T749C 000:825.478 - 0.030ms returns 2 (0x2)
T749C 000:825.486 JLINK_ReadMemEx(0x080001E4, 0x3C Bytes, Flags = 0x02000000)
T749C 000:825.493    -- Read from C cache (60 bytes @ 0x080001E4)
T749C 000:825.504   Data:  00 25 00 26 10 3A 28 BF 78 C1 FB D8 52 07 28 BF ...
T749C 000:825.514 - 0.028ms returns 60 (0x3C)
T749C 000:825.522 JLINK_ReadMemEx(0x080001E4, 0x2 Bytes, Flags = 0x02000000)
T749C 000:825.529    -- Read from C cache (2 bytes @ 0x080001E4)
T749C 000:825.539   Data:  00 25
T749C 000:825.550 - 0.027ms returns 2 (0x2)
T749C 000:825.558 JLINK_ReadMemEx(0x080001E4, 0x3C Bytes, Flags = 0x02000000)
T749C 000:825.565    -- Read from C cache (60 bytes @ 0x080001E4)
T749C 000:825.576   Data:  00 25 00 26 10 3A 28 BF 78 C1 FB D8 52 07 28 BF ...
T749C 000:825.586 - 0.027ms returns 60 (0x3C)
T749C 000:825.594 JLINK_ReadMemEx(0x080001E4, 0x2 Bytes, Flags = 0x02000000)
T749C 000:825.601    -- Read from C cache (2 bytes @ 0x080001E4)
T749C 000:825.611   Data:  00 25
T749C 000:825.621 - 0.027ms returns 2 (0x2)
T749C 000:825.630 JLINK_ReadMemEx(0x080001E6, 0x2 Bytes, Flags = 0x02000000)
T749C 000:825.639    -- Read from C cache (2 bytes @ 0x080001E6)
T749C 000:825.649   Data:  00 26
T749C 000:825.661 - 0.030ms returns 2 (0x2)
T749C 000:825.667 JLINK_ReadMemEx(0x080001E6, 0x2 Bytes, Flags = 0x02000000)
T749C 000:825.673    -- Read from C cache (2 bytes @ 0x080001E6)
T749C 000:825.681   Data:  00 26
T749C 000:825.690 - 0.022ms returns 2 (0x2)
T749C 000:825.696 JLINK_ReadMemEx(0x080001E8, 0x3C Bytes, Flags = 0x02000000)
T749C 000:825.702    -- Read from C cache (60 bytes @ 0x080001E8)
T749C 000:825.711   Data:  10 3A 28 BF 78 C1 FB D8 52 07 28 BF 30 C1 48 BF ...
T749C 000:825.719 - 0.022ms returns 60 (0x3C)
T749C 000:825.726 JLINK_ReadMemEx(0x080001E8, 0x2 Bytes, Flags = 0x02000000)
T749C 000:825.732    -- Read from C cache (2 bytes @ 0x080001E8)
T749C 000:825.740   Data:  10 3A
T749C 000:825.748 - 0.022ms returns 2 (0x2)
T749C 000:825.755 JLINK_ReadMemEx(0x080001E8, 0x3C Bytes, Flags = 0x02000000)
T749C 000:825.761    -- Read from C cache (60 bytes @ 0x080001E8)
T749C 000:825.769   Data:  10 3A 28 BF 78 C1 FB D8 52 07 28 BF 30 C1 48 BF ...
T749C 000:825.777 - 0.022ms returns 60 (0x3C)
T749C 000:825.784 JLINK_ReadMemEx(0x080001E8, 0x2 Bytes, Flags = 0x02000000)
T749C 000:825.790    -- Read from C cache (2 bytes @ 0x080001E8)
T749C 000:825.798   Data:  10 3A
T749C 000:825.806 - 0.022ms returns 2 (0x2)
T749C 000:825.813 JLINK_ReadMemEx(0x080001EA, 0x2 Bytes, Flags = 0x02000000)
T749C 000:825.819    -- Read from C cache (2 bytes @ 0x080001EA)
T749C 000:825.827   Data:  28 BF
T749C 000:825.836 - 0.022ms returns 2 (0x2)
T749C 000:825.843 JLINK_ReadMemEx(0x080001EA, 0x2 Bytes, Flags = 0x02000000)
T749C 000:825.848    -- Read from C cache (2 bytes @ 0x080001EA)
T749C 000:825.857   Data:  28 BF
T749C 000:825.865 - 0.022ms returns 2 (0x2)
T749C 000:825.871 JLINK_ReadMemEx(0x080001EC, 0x3C Bytes, Flags = 0x02000000)
T749C 000:825.877    -- Read from C cache (60 bytes @ 0x080001EC)
T749C 000:825.886   Data:  78 C1 FB D8 52 07 28 BF 30 C1 48 BF 0B 60 70 47 ...
T749C 000:825.894 - 0.022ms returns 60 (0x3C)
T749C 000:825.901 JLINK_ReadMemEx(0x080001EC, 0x2 Bytes, Flags = 0x02000000)
T749C 000:825.907    -- Read from C cache (2 bytes @ 0x080001EC)
T749C 000:825.915   Data:  78 C1
T749C 000:825.923 - 0.022ms returns 2 (0x2)
T749C 000:825.930 JLINK_ReadMemEx(0x080001EC, 0x3C Bytes, Flags = 0x02000000)
T749C 000:825.936    -- Read from C cache (60 bytes @ 0x080001EC)
T749C 000:825.944   Data:  78 C1 FB D8 52 07 28 BF 30 C1 48 BF 0B 60 70 47 ...
T749C 000:825.952 - 0.022ms returns 60 (0x3C)
T749C 000:825.959 JLINK_ReadMemEx(0x080001EC, 0x2 Bytes, Flags = 0x02000000)
T749C 000:825.965    -- Read from C cache (2 bytes @ 0x080001EC)
T749C 000:825.973   Data:  78 C1
T749C 000:825.981 - 0.022ms returns 2 (0x2)
T749C 000:825.988 JLINK_ReadMemEx(0x080001EE, 0x2 Bytes, Flags = 0x02000000)
T749C 000:825.996    -- Read from C cache (2 bytes @ 0x080001EE)
T749C 000:826.004   Data:  FB D8
T749C 000:826.013 - 0.024ms returns 2 (0x2)
T749C 000:826.019 JLINK_ReadMemEx(0x080001EE, 0x2 Bytes, Flags = 0x02000000)
T749C 000:826.025    -- Read from C cache (2 bytes @ 0x080001EE)
T749C 000:826.033   Data:  FB D8
T749C 000:826.042 - 0.022ms returns 2 (0x2)
T749C 000:826.048 JLINK_ReadMemEx(0x080001F0, 0x3C Bytes, Flags = 0x02000000)
T749C 000:826.054    -- Read from C cache (60 bytes @ 0x080001F0)
T749C 000:826.063   Data:  52 07 28 BF 30 C1 48 BF 0B 60 70 47 1F B5 03 F0 ...
T749C 000:826.071 - 0.022ms returns 60 (0x3C)
T749C 000:826.078 JLINK_ReadMemEx(0x080001F0, 0x2 Bytes, Flags = 0x02000000)
T749C 000:826.084    -- Read from C cache (2 bytes @ 0x080001F0)
T749C 000:826.092   Data:  52 07
T749C 000:826.100 - 0.022ms returns 2 (0x2)
T749C 000:826.107 JLINK_ReadMemEx(0x080001F0, 0x3C Bytes, Flags = 0x02000000)
T749C 000:826.113    -- Read from C cache (60 bytes @ 0x080001F0)
T749C 000:826.121   Data:  52 07 28 BF 30 C1 48 BF 0B 60 70 47 1F B5 03 F0 ...
T749C 000:826.129 - 0.022ms returns 60 (0x3C)
T749C 000:826.136 JLINK_ReadMemEx(0x080001F0, 0x2 Bytes, Flags = 0x02000000)
T749C 000:826.142    -- Read from C cache (2 bytes @ 0x080001F0)
T749C 000:826.150   Data:  52 07
T749C 000:826.158 - 0.022ms returns 2 (0x2)
T749C 000:826.165 JLINK_ReadMemEx(0x080001F2, 0x2 Bytes, Flags = 0x02000000)
T749C 000:826.171    -- Read from C cache (2 bytes @ 0x080001F2)
T749C 000:826.179   Data:  28 BF
T749C 000:826.187 - 0.022ms returns 2 (0x2)
T749C 000:826.194 JLINK_ReadMemEx(0x080001F2, 0x2 Bytes, Flags = 0x02000000)
T749C 000:826.200    -- Read from C cache (2 bytes @ 0x080001F2)
T749C 000:826.208   Data:  28 BF
T749C 000:826.216 - 0.022ms returns 2 (0x2)
T749C 000:826.223 JLINK_ReadMemEx(0x080001F4, 0x3C Bytes, Flags = 0x02000000)
T749C 000:826.229    -- Read from C cache (60 bytes @ 0x080001F4)
T749C 000:826.237   Data:  30 C1 48 BF 0B 60 70 47 1F B5 03 F0 EA FC 1F BD ...
T749C 000:826.246 - 0.022ms returns 60 (0x3C)
T749C 000:826.252 JLINK_ReadMemEx(0x080001F4, 0x2 Bytes, Flags = 0x02000000)
T749C 000:826.258    -- Read from C cache (2 bytes @ 0x080001F4)
T749C 000:826.266   Data:  30 C1
T749C 000:826.275 - 0.022ms returns 2 (0x2)
T749C 000:826.281 JLINK_ReadMemEx(0x080001F4, 0x3C Bytes, Flags = 0x02000000)
T749C 000:826.287    -- Read from C cache (60 bytes @ 0x080001F4)
T749C 000:826.296   Data:  30 C1 48 BF 0B 60 70 47 1F B5 03 F0 EA FC 1F BD ...
T749C 000:826.304 - 0.022ms returns 60 (0x3C)
T749C 000:826.311 JLINK_ReadMemEx(0x080001F4, 0x2 Bytes, Flags = 0x02000000)
T749C 000:826.317    -- Read from C cache (2 bytes @ 0x080001F4)
T749C 000:826.325   Data:  30 C1
T749C 000:826.333 - 0.022ms returns 2 (0x2)
T749C 000:826.340 JLINK_ReadMemEx(0x080001F6, 0x2 Bytes, Flags = 0x02000000)
T749C 000:826.346    -- Read from C cache (2 bytes @ 0x080001F6)
T749C 000:826.354   Data:  48 BF
T749C 000:826.362 - 0.022ms returns 2 (0x2)
T749C 000:826.369 JLINK_ReadMemEx(0x080001F6, 0x2 Bytes, Flags = 0x02000000)
T749C 000:826.375    -- Read from C cache (2 bytes @ 0x080001F6)
T749C 000:826.383   Data:  48 BF
T749C 000:826.392 - 0.022ms returns 2 (0x2)
T749C 000:826.398 JLINK_ReadMemEx(0x080001F8, 0x3C Bytes, Flags = 0x02000000)
T749C 000:826.404    -- Read from C cache (60 bytes @ 0x080001F8)
T749C 000:826.414   Data:  0B 60 70 47 1F B5 03 F0 EA FC 1F BD 10 B5 10 BD ...
T749C 000:826.422 - 0.023ms returns 60 (0x3C)
T749C 000:826.429 JLINK_ReadMemEx(0x080001F8, 0x2 Bytes, Flags = 0x02000000)
T749C 000:826.435    -- Read from C cache (2 bytes @ 0x080001F8)
T749C 000:826.443   Data:  0B 60
T749C 000:826.452 - 0.023ms returns 2 (0x2)
T749C 000:826.459 JLINK_ReadMemEx(0x080001F8, 0x3C Bytes, Flags = 0x02000000)
T749C 000:826.464    -- Read from C cache (60 bytes @ 0x080001F8)
T749C 000:826.473   Data:  0B 60 70 47 1F B5 03 F0 EA FC 1F BD 10 B5 10 BD ...
T749C 000:826.482 - 0.023ms returns 60 (0x3C)
T749C 000:826.488 JLINK_ReadMemEx(0x080001F8, 0x2 Bytes, Flags = 0x02000000)
T749C 000:826.496    -- Read from C cache (2 bytes @ 0x080001F8)
T749C 000:826.504   Data:  0B 60
T749C 000:826.513 - 0.024ms returns 2 (0x2)
T749C 000:826.519 JLINK_ReadMemEx(0x080001FA, 0x2 Bytes, Flags = 0x02000000)
T749C 000:826.525    -- Read from C cache (2 bytes @ 0x080001FA)
T749C 000:826.533   Data:  70 47
T749C 000:826.542 - 0.022ms returns 2 (0x2)
T749C 000:826.548 JLINK_ReadMemEx(0x080001FA, 0x2 Bytes, Flags = 0x02000000)
T749C 000:826.554    -- Read from C cache (2 bytes @ 0x080001FA)
T749C 000:826.562   Data:  70 47
T749C 000:826.570 - 0.022ms returns 2 (0x2)
T749C 000:826.577 JLINK_ReadMemEx(0x080001FC, 0x3C Bytes, Flags = 0x02000000)
T749C 000:826.583    -- Read from C cache (60 bytes @ 0x080001FC)
T749C 000:826.592   Data:  1F B5 03 F0 EA FC 1F BD 10 B5 10 BD 00 F0 68 F8 ...
T749C 000:826.600 - 0.023ms returns 60 (0x3C)
T749C 000:826.607 JLINK_ReadMemEx(0x080001FC, 0x2 Bytes, Flags = 0x02000000)
T749C 000:826.613    -- Read from C cache (2 bytes @ 0x080001FC)
T749C 000:826.621   Data:  1F B5
T749C 000:826.629 - 0.022ms returns 2 (0x2)
T749C 000:826.636 JLINK_ReadMemEx(0x080001FC, 0x3C Bytes, Flags = 0x02000000)
T749C 000:826.642    -- Read from C cache (60 bytes @ 0x080001FC)
T749C 000:826.651   Data:  1F B5 03 F0 EA FC 1F BD 10 B5 10 BD 00 F0 68 F8 ...
T749C 000:826.659 - 0.023ms returns 60 (0x3C)
T749C 000:826.666 JLINK_ReadMemEx(0x080001FC, 0x2 Bytes, Flags = 0x02000000)
T749C 000:826.671    -- Read from C cache (2 bytes @ 0x080001FC)
T749C 000:826.680   Data:  1F B5
T749C 000:826.688 - 0.022ms returns 2 (0x2)
T749C 000:826.694 JLINK_ReadMemEx(0x080001FE, 0x2 Bytes, Flags = 0x02000000)
T749C 000:826.700    -- Read from C cache (2 bytes @ 0x080001FE)
T749C 000:826.708   Data:  03 F0
T749C 000:826.717 - 0.022ms returns 2 (0x2)
T749C 000:826.723 JLINK_ReadMemEx(0x080001FE, 0x2 Bytes, Flags = 0x02000000)
T749C 000:826.729    -- Read from C cache (2 bytes @ 0x080001FE)
T749C 000:826.737   Data:  03 F0
T749C 000:826.746 - 0.022ms returns 2 (0x2)
T749C 000:826.752 JLINK_ReadMemEx(0x08000200, 0x3C Bytes, Flags = 0x02000000)
T749C 000:826.758    -- Read from C cache (60 bytes @ 0x08000200)
T749C 000:826.767   Data:  EA FC 1F BD 10 B5 10 BD 00 F0 68 F8 11 46 FF F7 ...
T749C 000:826.775 - 0.022ms returns 60 (0x3C)
T749C 000:826.782 JLINK_ReadMemEx(0x08000200, 0x2 Bytes, Flags = 0x02000000)
T749C 000:826.788    -- Read from C cache (2 bytes @ 0x08000200)
T749C 000:826.796   Data:  EA FC
T749C 000:826.804 - 0.022ms returns 2 (0x2)
T749C 000:826.811 JLINK_ReadMemEx(0x08000202, 0x2 Bytes, Flags = 0x02000000)
T749C 000:826.817    -- Read from C cache (2 bytes @ 0x08000202)
T749C 000:826.825   Data:  1F BD
T749C 000:826.834 - 0.023ms returns 2 (0x2)
T749C 000:826.841 JLINK_ReadMemEx(0x08000204, 0x3C Bytes, Flags = 0x02000000)
T749C 000:826.847    -- Read from C cache (60 bytes @ 0x08000204)
T749C 000:826.855   Data:  10 B5 10 BD 00 F0 68 F8 11 46 FF F7 F5 FF 03 F0 ...
T749C 000:826.863 - 0.022ms returns 60 (0x3C)
T749C 000:826.870 JLINK_ReadMemEx(0x08000204, 0x2 Bytes, Flags = 0x02000000)
T749C 000:826.876    -- Read from C cache (2 bytes @ 0x08000204)
T749C 000:826.885   Data:  10 B5
T749C 000:826.896 - 0.025ms returns 2 (0x2)
T749C 000:826.903 JLINK_ReadMemEx(0x08000204, 0x3C Bytes, Flags = 0x02000000)
T749C 000:826.909    -- Read from C cache (60 bytes @ 0x08000204)
T749C 000:826.917   Data:  10 B5 10 BD 00 F0 68 F8 11 46 FF F7 F5 FF 03 F0 ...
T749C 000:826.925 - 0.022ms returns 60 (0x3C)
T749C 000:826.932 JLINK_ReadMemEx(0x08000204, 0x2 Bytes, Flags = 0x02000000)
T749C 000:826.938    -- Read from C cache (2 bytes @ 0x08000204)
T749C 000:826.946   Data:  10 B5
T749C 000:826.954 - 0.022ms returns 2 (0x2)
T749C 000:826.961 JLINK_ReadMemEx(0x08000206, 0x2 Bytes, Flags = 0x02000000)
T749C 000:826.967    -- Read from C cache (2 bytes @ 0x08000206)
T749C 000:826.975   Data:  10 BD
T749C 000:826.983 - 0.022ms returns 2 (0x2)
T749C 000:826.990 JLINK_ReadMemEx(0x08000206, 0x2 Bytes, Flags = 0x02000000)
T749C 000:826.996    -- Read from C cache (2 bytes @ 0x08000206)
T749C 000:827.007   Data:  10 BD
T749C 000:827.015 - 0.025ms returns 2 (0x2)
T749C 000:827.022 JLINK_ReadMemEx(0x08000208, 0x3C Bytes, Flags = 0x02000000)
T749C 000:827.029   CPU_ReadMem(64 bytes @ 0x08000240)
T749C 000:830.890    -- Updating C cache (64 bytes @ 0x08000240)
T749C 000:830.912    -- Read from C cache (60 bytes @ 0x08000208)
T749C 000:830.924   Data:  00 F0 68 F8 11 46 FF F7 F5 FF 03 F0 FF F9 00 F0 ...
T749C 000:830.935 - 3.913ms returns 60 (0x3C)
T749C 000:830.946 JLINK_ReadMemEx(0x08000208, 0x2 Bytes, Flags = 0x02000000)
T749C 000:830.955    -- Read from C cache (2 bytes @ 0x08000208)
T749C 000:830.965   Data:  00 F0
T749C 000:830.975 - 0.029ms returns 2 (0x2)
T749C 000:830.985 JLINK_ReadMemEx(0x08000208, 0x3C Bytes, Flags = 0x02000000)
T749C 000:830.993    -- Read from C cache (60 bytes @ 0x08000208)
T749C 000:831.004   Data:  00 F0 68 F8 11 46 FF F7 F5 FF 03 F0 FF F9 00 F0 ...
T749C 000:831.014 - 0.028ms returns 60 (0x3C)
T749C 000:831.022 JLINK_ReadMemEx(0x08000208, 0x2 Bytes, Flags = 0x02000000)
T749C 000:831.029    -- Read from C cache (2 bytes @ 0x08000208)
T749C 000:831.039   Data:  00 F0
T749C 000:831.049 - 0.027ms returns 2 (0x2)
T749C 000:831.058 JLINK_ReadMemEx(0x0800020A, 0x2 Bytes, Flags = 0x02000000)
T749C 000:831.065    -- Read from C cache (2 bytes @ 0x0800020A)
T749C 000:831.075   Data:  68 F8
T749C 000:831.086 - 0.027ms returns 2 (0x2)
T749C 000:831.094 JLINK_ReadMemEx(0x0800020C, 0x3C Bytes, Flags = 0x02000000)
T749C 000:831.102    -- Read from C cache (60 bytes @ 0x0800020C)
T749C 000:831.112   Data:  11 46 FF F7 F5 FF 03 F0 FF F9 00 F0 86 F8 03 B4 ...
T749C 000:831.122 - 0.028ms returns 60 (0x3C)
T749C 000:831.131 JLINK_ReadMemEx(0x0800020C, 0x2 Bytes, Flags = 0x02000000)
T749C 000:831.138    -- Read from C cache (2 bytes @ 0x0800020C)
T749C 000:831.148   Data:  11 46
T749C 000:831.158 - 0.027ms returns 2 (0x2)
T749C 000:831.166 JLINK_ReadMemEx(0x0800020E, 0x2 Bytes, Flags = 0x02000000)
T749C 000:831.174    -- Read from C cache (2 bytes @ 0x0800020E)
T749C 000:831.184   Data:  FF F7
T749C 000:831.194 - 0.027ms returns 2 (0x2)
T749C 000:831.202 JLINK_ReadMemEx(0x0800020E, 0x2 Bytes, Flags = 0x02000000)
T749C 000:831.209    -- Read from C cache (2 bytes @ 0x0800020E)
T749C 000:831.219   Data:  FF F7
T749C 000:831.229 - 0.027ms returns 2 (0x2)
T749C 000:831.237 JLINK_ReadMemEx(0x08000210, 0x3C Bytes, Flags = 0x02000000)
T749C 000:831.245    -- Read from C cache (60 bytes @ 0x08000210)
T749C 000:831.255   Data:  F5 FF 03 F0 FF F9 00 F0 86 F8 03 B4 FF F7 F2 FF ...
T749C 000:831.265 - 0.027ms returns 60 (0x3C)
T749C 000:831.273 JLINK_ReadMemEx(0x08000210, 0x2 Bytes, Flags = 0x02000000)
T749C 000:831.281    -- Read from C cache (2 bytes @ 0x08000210)
T749C 000:831.291   Data:  F5 FF
T749C 000:831.301 - 0.027ms returns 2 (0x2)
T749C 000:831.309 JLINK_ReadMemEx(0x08000212, 0x2 Bytes, Flags = 0x02000000)
T749C 000:831.317    -- Read from C cache (2 bytes @ 0x08000212)
T749C 000:831.326   Data:  03 F0
T749C 000:831.337 - 0.027ms returns 2 (0x2)
T749C 000:831.345 JLINK_ReadMemEx(0x08000214, 0x3C Bytes, Flags = 0x02000000)
T749C 000:831.352    -- Read from C cache (60 bytes @ 0x08000214)
T749C 000:831.363   Data:  FF F9 00 F0 86 F8 03 B4 FF F7 F2 FF 03 BC 03 F0 ...
T749C 000:831.373 - 0.028ms returns 60 (0x3C)
T749C 000:831.382 JLINK_ReadMemEx(0x08000214, 0x2 Bytes, Flags = 0x02000000)
T749C 000:831.389    -- Read from C cache (2 bytes @ 0x08000214)
T749C 000:831.399   Data:  FF F9
T749C 000:831.409 - 0.027ms returns 2 (0x2)
T749C 000:831.417 JLINK_ReadMemEx(0x08000216, 0x2 Bytes, Flags = 0x02000000)
T749C 000:831.425    -- Read from C cache (2 bytes @ 0x08000216)
T749C 000:831.435   Data:  00 F0
T749C 000:831.445 - 0.027ms returns 2 (0x2)
T749C 000:831.453 JLINK_ReadMemEx(0x08000218, 0x3C Bytes, Flags = 0x02000000)
T749C 000:831.460    -- Read from C cache (60 bytes @ 0x08000218)
T749C 000:831.471   Data:  86 F8 03 B4 FF F7 F2 FF 03 BC 03 F0 63 F9 00 00 ...
T749C 000:831.481 - 0.028ms returns 60 (0x3C)
T749C 000:831.493 JLINK_ReadMemEx(0x08000218, 0x2 Bytes, Flags = 0x02000000)
T749C 000:831.502    -- Read from C cache (2 bytes @ 0x08000218)
T749C 000:831.512   Data:  86 F8
T749C 000:831.522 - 0.029ms returns 2 (0x2)
T749C 000:831.531 JLINK_ReadMemEx(0x0800021A, 0x2 Bytes, Flags = 0x02000000)
T749C 000:831.538    -- Read from C cache (2 bytes @ 0x0800021A)
T749C 000:831.548   Data:  03 B4
T749C 000:831.558 - 0.027ms returns 2 (0x2)
T749C 000:831.566 JLINK_ReadMemEx(0x0800021C, 0x3C Bytes, Flags = 0x02000000)
T749C 000:831.573    -- Read from C cache (60 bytes @ 0x0800021C)
T749C 000:831.584   Data:  FF F7 F2 FF 03 BC 03 F0 63 F9 00 00 09 48 80 47 ...
T749C 000:831.594 - 0.028ms returns 60 (0x3C)
T749C 000:831.602 JLINK_ReadMemEx(0x0800021C, 0x2 Bytes, Flags = 0x02000000)
T749C 000:831.609    -- Read from C cache (2 bytes @ 0x0800021C)
T749C 000:831.619   Data:  FF F7
T749C 000:831.631 - 0.028ms returns 2 (0x2)
T749C 000:831.642 JLINK_ReadMemEx(0x0800021C, 0x3C Bytes, Flags = 0x02000000)
T749C 000:831.649    -- Read from C cache (60 bytes @ 0x0800021C)
T749C 000:831.660   Data:  FF F7 F2 FF 03 BC 03 F0 63 F9 00 00 09 48 80 47 ...
T749C 000:831.670 - 0.028ms returns 60 (0x3C)
T749C 000:831.678 JLINK_ReadMemEx(0x0800021C, 0x2 Bytes, Flags = 0x02000000)
T749C 000:831.685    -- Read from C cache (2 bytes @ 0x0800021C)
T749C 000:831.695   Data:  FF F7
T749C 000:831.706 - 0.027ms returns 2 (0x2)
T749C 000:831.714 JLINK_ReadMemEx(0x0800021E, 0x2 Bytes, Flags = 0x02000000)
T749C 000:831.721    -- Read from C cache (2 bytes @ 0x0800021E)
T749C 000:831.731   Data:  F2 FF
T749C 000:831.749 - 0.034ms returns 2 (0x2)
T749C 000:831.757 JLINK_ReadMemEx(0x08000220, 0x3C Bytes, Flags = 0x02000000)
T749C 000:831.763    -- Read from C cache (60 bytes @ 0x08000220)
T749C 000:831.772   Data:  03 BC 03 F0 63 F9 00 00 09 48 80 47 09 48 00 47 ...
T749C 000:831.781 - 0.023ms returns 60 (0x3C)
T749C 000:831.788 JLINK_ReadMemEx(0x08000220, 0x2 Bytes, Flags = 0x02000000)
T749C 000:831.794    -- Read from C cache (2 bytes @ 0x08000220)
T749C 000:831.803   Data:  03 BC
T749C 000:831.811 - 0.023ms returns 2 (0x2)
T749C 000:831.818 JLINK_ReadMemEx(0x08000222, 0x2 Bytes, Flags = 0x02000000)
T749C 000:831.825    -- Read from C cache (2 bytes @ 0x08000222)
T749C 000:831.833   Data:  03 F0
T749C 000:831.842 - 0.023ms returns 2 (0x2)
T749C 000:831.849 JLINK_ReadMemEx(0x08000222, 0x2 Bytes, Flags = 0x02000000)
T749C 000:831.855    -- Read from C cache (2 bytes @ 0x08000222)
T749C 000:831.863   Data:  03 F0
T749C 000:831.873 - 0.023ms returns 2 (0x2)
T749C 000:831.881 JLINK_ReadMemEx(0x08000224, 0x3C Bytes, Flags = 0x02000000)
T749C 000:831.889    -- Read from C cache (60 bytes @ 0x08000224)
T749C 000:831.898   Data:  63 F9 00 00 09 48 80 47 09 48 00 47 FE E7 FE E7 ...
T749C 000:831.906 - 0.025ms returns 60 (0x3C)
T749C 000:831.913 JLINK_ReadMemEx(0x08000224, 0x2 Bytes, Flags = 0x02000000)
T749C 000:831.919    -- Read from C cache (2 bytes @ 0x08000224)
T749C 000:831.928   Data:  63 F9
T749C 000:831.936 - 0.023ms returns 2 (0x2)
T749C 000:831.943 JLINK_ReadMemEx(0x08000226, 0x2 Bytes, Flags = 0x02000000)
T749C 000:831.950    -- Read from C cache (2 bytes @ 0x08000226)
T749C 000:831.958   Data:  00 00
T749C 000:831.967 - 0.023ms returns 2 (0x2)
T749C 000:831.974 JLINK_ReadMemEx(0x08000228, 0x3C Bytes, Flags = 0x02000000)
T749C 000:831.980    -- Read from C cache (60 bytes @ 0x08000228)
T749C 000:831.989   Data:  09 48 80 47 09 48 00 47 FE E7 FE E7 FE E7 FE E7 ...
T749C 000:831.997 - 0.023ms returns 60 (0x3C)
T749C 000:832.005 JLINK_ReadMemEx(0x08000228, 0x2 Bytes, Flags = 0x02000000)
T749C 000:832.011    -- Read from C cache (2 bytes @ 0x08000228)
T749C 000:832.019   Data:  09 48
T749C 000:832.028 - 0.023ms returns 2 (0x2)
T749C 000:832.046 JLINK_ReadMemEx(0x08000228, 0x3C Bytes, Flags = 0x02000000)
T749C 000:832.052    -- Read from C cache (60 bytes @ 0x08000228)
T749C 000:832.061   Data:  09 48 80 47 09 48 00 47 FE E7 FE E7 FE E7 FE E7 ...
T749C 000:832.069 - 0.024ms returns 60 (0x3C)
T749C 000:832.076 JLINK_ReadMemEx(0x08000228, 0x2 Bytes, Flags = 0x02000000)
T749C 000:832.085    -- Read from C cache (2 bytes @ 0x08000228)
T749C 000:832.093   Data:  09 48
T749C 000:832.102 - 0.025ms returns 2 (0x2)
T749C 000:832.109 JLINK_ReadMemEx(0x0800022A, 0x2 Bytes, Flags = 0x02000000)
T749C 000:832.115    -- Read from C cache (2 bytes @ 0x0800022A)
T749C 000:832.123   Data:  80 47
T749C 000:832.132 - 0.023ms returns 2 (0x2)
T749C 000:832.142 JLINK_ReadMemEx(0x0800022A, 0x2 Bytes, Flags = 0x02000000)
T749C 000:832.149    -- Read from C cache (2 bytes @ 0x0800022A)
T749C 000:832.157   Data:  80 47
T749C 000:832.166 - 0.023ms returns 2 (0x2)
T749C 000:832.173 JLINK_ReadMemEx(0x0800022C, 0x3C Bytes, Flags = 0x02000000)
T749C 000:832.179    -- Read from C cache (60 bytes @ 0x0800022C)
T749C 000:832.188   Data:  09 48 00 47 FE E7 FE E7 FE E7 FE E7 FE E7 FE E7 ...
T749C 000:832.196 - 0.023ms returns 60 (0x3C)
T749C 000:832.203 JLINK_ReadMemEx(0x0800022C, 0x2 Bytes, Flags = 0x02000000)
T749C 000:832.209    -- Read from C cache (2 bytes @ 0x0800022C)
T749C 000:832.217   Data:  09 48
T749C 000:832.226 - 0.022ms returns 2 (0x2)
